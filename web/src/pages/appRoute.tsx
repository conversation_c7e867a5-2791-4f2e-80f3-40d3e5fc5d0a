import React from 'react';
import { Route, Redirect, Switch } from 'react-router-dom';
import { CorePage404, CorePage500 } from '@sharkr/components';
import { UmcAuthGuard } from '@eagler/authorizion';

import { Home } from './home';
import { PutGoods } from './channel';
import { StmosphereList, TemplateLists, PersonalList, GalleryList, StmosphereDetail } from './stmosphereManage'

import { TemplateList } from './activity'

export const AppRoute: React.FC = () => (
  <Switch>
    <Route component={Home} key="/home" path="/home" />
    <Route
      key="/atmosphere/list"
      path="/atmosphere/list"
      render={() => <UmcAuthGuard component={StmosphereList} />}
    />
    <Route
      key="/gallery/list"
      path="/gallery/list"
      render={() => <UmcAuthGuard component={GalleryList} />}
    />
    <Route
      key="/gallery/detail"
      path="/gallery/detail"
      render={() => <UmcAuthGuard component={StmosphereDetail} />}
    />
    <Route
      component={TemplateLists}
      key="/templatesList/list"
      path="/templatesList/list"
      render={() => <UmcAuthGuard component={TemplateLists} />}
    />
    <Route
      component={PersonalList}
      key="/personalList/list"
      path="/personalList/list"
      render={() => <UmcAuthGuard component={PersonalList} />}
    />
    <Route
      key="/activity/template/list"
      path="/activity/template/list"
      render={() => <UmcAuthGuard component={TemplateList} />}
    />
    <Route
      key="/channel/putGoods"
      path="/channel/putGoods"
      render={() => <UmcAuthGuard component={PutGoods} />}
    />
    <Redirect exact from="/" to="/home" />
    <Route component={CorePage500} key="/500" path="/500" />
    <Route component={CorePage404} />
  </Switch>
);
